<?php namespace App\Http\Controllers;

use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Response;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use Spa<PERSON>\SchemaOrg\Schema;

class PagesController extends Controller {

    public function __construct()
    {
        parent::__construct();

        // canonical url initialization
        $this->view_data['canonical_url'] = Request::url();
    }

	/**
	 * About us page
	 */
	public function about()
	{
		$this->view_data['about_selected'] = 'current';

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.about'),
                            )
                        ),
                )
            );

        // open graph
        $this->overwriteOpengraph(
            trans('about.page_title'),
            trans('about.meta_description')
        );

		return Response::view('frontend.about', $this->view_data);
	}

	/**
	 * Offers page
	 */
	public function offers()
	{
        // after the great redesign of 2022 we no longer want an offers page
        return redirect()->route('home');
	}

	/**
	 * Crete page
	 */
	public function crete()
	{
		return redirect()->route('home');
	}

	/**
	 * Services page
	 */
	public function services()
	{
        $this->view_data['services_selected'] = 'current';

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.services'),
                            )
                        ),
                )
            );

        // opengraph
        $this->overwriteOpengraph(
            trans('services.page_title'),
            trans('services.meta_description')
        );

		return Response::view('frontend.services', $this->view_data);
	}

	/**
	 * Branches page
	 */
	public function branches()
	{
	    // after the great redesign of 2022 we no longer want a branches page
        return redirect()->route('home');
	}

	/**
	 * FAQ page
	 */
	public function faq()
	{
        $this->view_data['faq_selected'] = 'current';

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.faq'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as
        $item_array = trans('faq.text');

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name(strip_tags($item['q']))
                ->acceptedAnswer(Schema::answer()
                    ->text(strip_tags($item['a']))
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // opengraph
        $this->overwriteOpengraph(
            trans('faq.page_title'),
            trans('faq.meta_description')
        );

		return Response::view('frontend.faq', $this->view_data);
	}

	/**
	 * Policy page
	 */
	public function policy()
	{
        $this->view_data['policy_selected'] = 'current';

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.policy'),
                            )
                        ),
                )
            );

        // opengraph
        $this->overwriteOpengraph(
            trans('policy.page_title'),
            trans('policy.meta_description')
        );

		return Response::view('frontend.policy', $this->view_data);
	}

	/**
	 * Privacy page
	 */
	public function privacy()
	{
        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.privacy'),
                            )
                        ),
                )
            );
		return Response::view('frontend.privacy', $this->view_data);
	}

    /**
     * Travelling safely page
     */
    public function safety()
    {
        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.safety'),
                            )
                        ),
                )
            );
        $this->view_data['safety_selected'] = 'current';

        return Response::view('frontend.safety', $this->view_data);
    }

    /**
     * Travelling safely page
     */
    public function insurance()
    {
        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.insurance'),
                            )
                        ),
                )
            );
//        $this->view_data['safety_selected'] = 'current';

        // opengraph
        $this->overwriteOpengraph(
            trans('insurance.page_title'),
            trans('insurance.meta_description')
        );

        return Response::view('frontend.insurance', $this->view_data);
    }

}
