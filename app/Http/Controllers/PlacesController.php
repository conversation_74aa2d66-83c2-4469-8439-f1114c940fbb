<?php namespace App\Http\Controllers;

use App\Category;
use App\GoogleReview;
use App\Location;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Response;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use <PERSON><PERSON>\SchemaOrg\Schema;

class PlacesController extends Controller
{

    /**
     * @var array marker_indexes
     * This is just a mapping of the marker number and the location for the custom map we show at localized pages.
     * According to this mapping array we will send for each location to the view the indexes of the markers we want to show
     * It is not actually used by any code
     */
    private $marker_indexes = array(
        1 => 'Heraklion',
        2 => 'Heraklion Airport Station',
        3 => 'Chania Airport Station',
        4 => 'Agios Nikolaos Port',
        5 => 'Rethymno Office',
        6 => 'Matala Office',
        7 => 'Siteia Office',
        8 => 'Chania',
    );

    public function __construct()
    {
        parent::__construct();

        // initialize some data and override/set on demand
        $this->view_data['background_image'] = '';
        $this->view_data['weather_city'] = '';
        $this->view_data['markers_indexes'] = [];
        $this->view_data['featuredListings'] = [];
        $this->view_data['place'] = '';
        $this->view_data['show_google_map'] = false;

        // css class in the main menu
        $this->view_data['localised_selected'] = 'current';

        // canonical url initialization
        $this->view_data['canonical_url'] = Request::url();

        // json-ld microdata script (echoed in view)
        $this->view_data['local_business_schema_localised_pages'] = Schema::localBusiness()
            ->url(route('home'))
            ->logo(asset('images/logo.svg'))
            ->image(asset('images/logo.svg'))
            ->name(trans('common.site_name'))
            ->email(config('schema_org.local_business.email_address'))
            ->priceRange(config('schema_org.local_business.price_range'))
            ->hasMap(config('schema_org.local_business.google_maps_place'))
            ->address(Schema::postalAddress()
                ->addressCountry(config('schema_org.local_business.address.country'))
                ->addressRegion(config('schema_org.local_business.address.region'))
                ->addressLocality(config('schema_org.local_business.address.locality'))
                ->postalCode(config('schema_org.local_business.address.postal_code'))
                ->streetAddress(config('schema_org.local_business.address.street_address'))
            )
            ->telephone(trans('common.phone'))
            ->openingHours(config('schema_org.local_business.opening_hours'));
    }


    /**
	 * Heraklion page
	 */
	public function heraklion()
	{
        // fetch featured listings fer teh current area
        $featuredCategory = Category::where(['name' => 'heraklion_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/heraklion_HERO.jpg';

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/heraklion.sections');

        // url referral to track clicks to featured listings
        $this->view_data['url_ref'] = 'heraklion';

        // for the pickup/dropoff dropdowns - pass the name of the location as stored in db
        $this->setDefaultLocation('Heraklion port');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,2,6]; // heraklion, heraklion airport, matala

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Heraklion,Greece';

        // google reviews
        // initialize location ids array for the location
        $location_ids = array();
        for($i = 2; $i <= 17; $i++)
        {
            $location_ids[] = $i;
        }

        $this->view_data['showable_reviews'] = GoogleReview::whereIn('location_id', $location_ids)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.heraklion'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.heraklion.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.heraklion.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.heraklion'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.heraklion.faq_1'), trans('faq.localised_homepages.heraklion.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/heraklion.page_title'),
            trans('places/heraklion.meta_description')
        );

        return Response::view('frontend.places.heraklion', $this->view_data);
	}

    /**
	 * Herakleion airport page
	 */
	public function heraklion_airport()
	{
        $this->view_data['show_google_map'] = true;

        // fetch featured listings fer teh current area
        $featuredCategory = Category::where(['name' => 'heraklion_airport_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/heraklion_airport_HERO4.jpg';

        // url referral to track clicks to featured listings
        $this->view_data['url_ref'] = 'heraklion_airport';

        // for the pickup/dropoff dropdowns - pass the name of the location as stored in db
        $this->setDefaultLocation('Heraklion airport');

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/heraklion_airport.sections');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,2,6]; // heraklion, heraklion airport, matala

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Heraklion,Greece';

        $this->view_data['airport_image'] = 'images/crete/heraklion_airport.jpg'; // relative to public folder

        // google reviews
        $this->view_data['showable_reviews'] = GoogleReview::where('location_id', 1)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.heraklion_airport'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.heraklion_airport.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.heraklion_airport.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => route('heraklion'),
                                'name' => trans('breadcrumbs.locations.heraklion'),
                            )
                        ),
                    Schema::listItem()
                        ->position(3)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.heraklion_airport'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.heraklion_airport.faq_1'), trans('faq.localised_homepages.heraklion_airport.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/heraklion_airport.page_title'),
            trans('places/heraklion_airport.meta_description')
        );

		return Response::view('frontend.places.heraklion_airport', $this->view_data);
	}

    /**
	 * Chania airport page
	 */
	public function chania_airport()
	{
        // fetch featured listings fer teh current area
        $featuredCategory = Category::where(['name' => 'chania_airport_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/chania-airport-HERO.jpg';

        // url referral to track clicks to featured listings
        $this->view_data['url_ref'] = 'chania_airport';

        // for the pickup/dropoff dropdowns - pass the name of the location as stored in db
        $this->setDefaultLocation('Chania airport');

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/chania_airport.sections');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,3,8]; // heraklion, chania airport, chania

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Chania,Greece';

        $this->view_data['airport_image'] = 'images/crete/chania_airport.jpg'; // relative to public folder

        // google reviews
        $this->view_data['showable_reviews'] = GoogleReview::where('location_id', 18)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.chania_airport'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.chania_airport.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.chania_airport.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => route('chania'),
                                'name' => trans('breadcrumbs.locations.chania'),
                            )
                        ),
                    Schema::listItem()
                        ->position(3)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.chania_airport'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.chania_airport.faq_1'), trans('faq.localised_homepages.chania_airport.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/chania_airport.page_title'),
            trans('places/chania_airport.meta_description')
        );

        return Response::view('frontend.places.chania_airport', $this->view_data);

	}

    /**
	 * Chania page
	 */
	public function chania()
	{
        // fetch featured listings fer teh current area
        $featuredCategory = Category::where(['name' => 'chania_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/chania.sections');

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/chania-HERO.jpg';

        // url referral to track clicks to featured listings
        $this->view_data['url_ref'] = 'chania';

        // for the pickup/dropoff dropdowns - pass the name of the location as stored in db
        $this->setDefaultLocation('Chania port');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,3,8]; // heraklion, chania airport, chania

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Chania,Greece';

        // google reviews
        // initialize location ids array for the location
        $location_ids = array();
        for($i = 19; $i <= 23; $i++)
        {
            $location_ids[] = $i;
        }

        $this->view_data['showable_reviews'] = GoogleReview::whereIn('location_id', $location_ids)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.chania'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.chania.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.chania.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.chania'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.chania.faq_1'), trans('faq.localised_homepages.chania.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/chania.page_title'),
            trans('places/chania.meta_description')
        );

		return Response::view('frontend.places.chania', $this->view_data);
	}


    /**
	 * Rethymno page
	 */
	public function rethymno()
	{
        $featuredCategory = Category::where(['name' => 'rethymno_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        // for the pickup/dropoff dropdowns - pass the name of the location as stored in db
        $this->setDefaultLocation('Rethymno port');

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/rethymno-HERO.jpg';

        // url referral to track clicks to featured listings
        $this->view_data['url_ref'] = 'rethymno';

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/rethymno.sections');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,5]; // heraklion, rethymno

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Rethymno,Greece';

        $this->view_data['place'] = 'rethymno';

        // google reviews
        // initialize location ids array for the location
        $location_ids = array();
        for($i = 24; $i <= 31; $i++)
        {
            $location_ids[] = $i;
        }

        $this->view_data['showable_reviews'] = GoogleReview::whereIn('location_id', $location_ids)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.rethymno'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.rethymno.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.rethymno.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.rethymno'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.rethymno.faq_1'), trans('faq.localised_homepages.rethymno.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/rethymno.page_title'),
            trans('places/rethymno.meta_description')
        );

		return Response::view('frontend.places.rethymno', $this->view_data);
	}


    /**
	 * Agios  Nikolaos
	 */
	public function agios_nikolaos()
	{

	    //TODO : set proper featured category name
        $featuredCategory = Category::where(['name' => 'agios_nikolaos_featured'])->first();
        if($featuredCategory)
        {
            $this->view_data['featuredListings'] = $featuredCategory->publishedListings()->getResults();
        }

        $this->setDefaultLocation('Lassithi Agios Nikolaos port');

        // configure teh bg image
        $this->view_data['background_image'] = '/images/crete/AgNikolaos-HERO2.jpg';

        // url referral to track clicks to featured listings TODO don't know if this is correct
        $this->view_data['url_ref'] = 'agios_nikolaos';

        // the lang items of the main body page sections
        $this->view_data['sections'] = Lang::get('places/agios_nikolaos.sections');

        // the markers we will show at custom map
        // check $marker_indexes class variable on top to see to which area each index refers
        $this->view_data['markers_indexes'] = [1,4, 7]; // heraklion, agios nikolaos, siteia

        // for the weather api to show the weather in proper city
        $this->view_data['weather_city'] = 'Agios+Nikolaos,GR';

        $this->view_data['place'] = 'agios_nikolaos';

        // google reviews
        // initialize location ids array for the location
        $location_ids = array();
        for($i = 32; $i <= 35; $i++)
        {
            $location_ids[] = $i;
        }

        $this->view_data['showable_reviews'] = GoogleReview::whereIn('location_id', $location_ids)
            ->showable()
            ->orderBy('review_rating', 'desc')
            ->orderBy('review_datetime', 'desc')
            ->take(9)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['product_schema'] = Schema::product()
            ->name(trans('common.product_name.agios_nikolaos'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.agios_nikolaos.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.agios_nikolaos.rating_value'))
            );

        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.locations.agios_nikolaos'),
                            )
                        ),
                )
            );

        // grab the lang items that hold the q and as and merge them in to one
        $item_array = array_merge(trans('faq.localised_homepages.agios_nikolaos.faq_1'), trans('faq.localised_homepages.agios_nikolaos.faq_2'));

        // initialization of vars
        $question_array = array();

        // population of the question array (holds the question schema objects)
        foreach($item_array as $item)
        {
            $question_array[] = Schema::question()
                ->name($item['q'])
                ->acceptedAnswer(Schema::answer()
                    ->text($item['a'])
                );
        }

        // json-ld microdata script (echoed in view)
        $this->view_data['faq_schema'] = Schema::fAQPage()
            ->inLanguage(LaravelLocalization::getCurrentLocale())
            ->url(Request::url())
            ->mainEntity($question_array);

        // open graph
        $this->overwriteOpengraph(
            trans('places/agios_nikolaos.page_title'),
            trans('places/agios_nikolaos.meta_description')
        );

        return Response::view('frontend.places.agios_nikolaos', $this->view_data);
	}

    /**
     * Sets the default location id in a view param in order for the js to pick it up and force preselected options
     * at the pickup/dropoff dropdowns
     *
     * @param $db_area_name // the name of the location as stored at the `locations` db table
     */
    private function setDefaultLocation($db_area_name = false)
    {
        $location = Location::where('name', $db_area_name)->first();
        $this->view_data['location_default'] =  $location ? $location->id : 0;
    }

}
