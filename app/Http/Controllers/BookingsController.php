<?php namespace App\Http\Controllers;

use App\Listing;
use App\Services\Offer\DateRange;
use App\Supergroup;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Response;

class BookingsController extends Controller {

    /**
     * Show the landing booking page
     *
     * @return mixed
     */
	public function showBooking()
	{
        // initial setup of cookie
        $this->initialSetupOfCookie();

        // seo stuff
        // canonical url
        $this->view_data['canonical_url'] = \Illuminate\Support\Facades\Request::url();

        // open graph
        $this->overwriteOpengraph(
            trans('bookings.landing.meta_title'),
            trans('bookings.landing.meta_description')
        );

		return Response::view('frontend.bookings.show', $this->view_data);
	}

    /**
     * Handles the submission of the contact form
     *
     * @return mixed
     */
	public function handleBooking()
	{
        // initial setup of cookie
        $this->initialSetupOfCookie();

        return redirect()
            ->route('booking.fleet.index', [
                'pul' => Arr::get($_COOKIE, 'pickup_location', 1),
                'pud' => Arr::get($_COOKIE, 'pickup_date', \Carbon\Carbon::now()->addDays(2)->format('d-m-Y')),
                'put' => Arr::get($_COOKIE, 'pickup_time', '12:00'),
                'dol' => Arr::get($_COOKIE, 'dropoff_location', 1),
                'dod' => Arr::get($_COOKIE, 'dropoff_date', \Carbon\Carbon::now()->addDays(9)->format('d-m-Y')),
                'dot' => Arr::get($_COOKIE, 'dropoff_time', '12:00'),
            ])
            ->with('x-bkng-rdrct', mt_rand());
	}

    /**
     * Show the fleet booking page
     */
    public function fleetIndex(Request $request)
    {
        if(
            $request->session()->missing('x-bkng-rdrct')
            && ! $request->has(['pul', 'pud', 'put', 'dol', 'dod', 'dot'])
        )
        {
            return redirect()->route('booking.show');
        }

        $this->initialSetupOfCookie();

//        dd($_COOKIE);

        // if null, set pickup location session var to default location id
        if(!Arr::get($_COOKIE, 'pickup_location'))
        {
            //set as session value the first key of the locations_dropdown_pickup array
            reset($this->view_data['locations_dropdown_pickup']);
            setcookie('pickup_location', key($this->view_data['locations_dropdown_pickup']), 0, '/');
        }

        // fetch the flagship listing per group
        $listings_temp = Listing::publishedEurodollar()
            ->join('groups', 'listings.group_id', '=', 'groups.id')
            ->orderBy('groups.group_order', 'asc')
//            ->orderBy('groups.name', 'asc')
            ->orderBy('group_flagship', 'desc')
            ->get()
            ->unique('group_id');

        // hack to bypass the malformed collection results that are returned above
        $listings_array = array();
        // we traverse the results and reload the listings anew in order to have a collection with UNmalformed listings objects
        foreach ($listings_temp->toArray() as $listing_item)
        {
            $listings_array[] = Listing::where('slug', '=', $listing_item['slug'])->first();
        }

        $this->view_data['listings'] = collect($listings_array);

        // get supergroups list
        $supergroups = Supergroup::with('translations')
            ->get()
            ->mapWithKeys(function ($supergroup)
            {
                return [$supergroup->id => $supergroup->title];
            });

        $this->view_data['supergroups'] = $supergroups;

        // get date range
        $pickup_date_time = Arr::get($_COOKIE, 'pickup_date');
        if(!empty($pickup_date_time)){
            $pickup_date_time .= ' ' . $this->view_data['pickup_time'];
        }
        $dropoff_date_time = Arr::get($_COOKIE, 'dropoff_date');
        if(!empty($dropoff_date_time)){
            $dropoff_date_time .= ' ' . $this->view_data['dropoff_time'];
        }
        $this->view_data['date_range'] = new DateRange($pickup_date_time, $dropoff_date_time);

        // handle the dropoff location for offer price calculation which takes place in the view (case cookie value is empty or not set)
        $this->view_data['dropoff_location_for_offer_calculation'] = !empty(Arr::get($_COOKIE, 'dropoff_location')) ?
            Arr::get($_COOKIE, 'dropoff_location') :
            Arr::get($_COOKIE, 'pickup_location', 1);

        // seo stuff
        // canonical url is set as the vanilla url (no params)
        $this->view_data['canonical_url'] = route('booking.fleet.index');

        return Response::view('frontend.bookings.index', $this->view_data);
    }

    /**
     * @return void
     */
    private function initialSetupOfCookie(): void
    {
        if (!isset($_COOKIE['pickup_date'])) {
            setcookie('pickup_date', \Carbon\Carbon::now()->addDays(2)->format('d-m-Y'), time()+86400*30*6, '/');
        }

        if (!isset($_COOKIE['dropoff_date'])) {
            setcookie('dropoff_date', \Carbon\Carbon::now()->addDays(9)->format('d-m-Y'), time()+86400*30*6, '/' );
        }

        if (!isset($_COOKIE['pickup_time'])) {
            setcookie('pickup_time', '12:00', time() + 86400*30*6, '/');
        }

        if (!isset($_COOKIE['dropoff_time'])) {
            setcookie('dropoff_time', '12:00', time() + 86400*30*6, '/');
        }

        if (!isset($_COOKIE['pickup_location'])) {
            setcookie('pickup_location', '1', time() + 86400*30*6, '/');
//            $_COOKIE['pickup_location'] = '1';
        }

        if (!isset($_COOKIE['dropoff_location'])) {
            setcookie('dropoff_location', '1', time() + 86400*30*6, '/');
//            $_COOKIE['dropoff_location'] = '1';
        }

        // align cookie indexes with url params
        if (isset($_GET['pul']))
        {
//            dd($_GET['pul']);
            setcookie('pickup_location', $_GET['pul'], time() + 86400*30*6, '/');
        }
        if (isset($_GET['pud'])) {
//            dd($_GET['pud']);
            setcookie('pickup_date', $_GET['pud'], time() + 86400*30*6, '/');
        }
        if (isset($_GET['put'])) {
//            dd($_GET['put']);
            setcookie('pickup_time', $_GET['put'], time() + 86400*30*6, '/');
        }
        if (isset($_GET['dol'])) {
//            dd($_GET['dol']);
            setcookie('dropoff_location', $_GET['dol'], time() + 86400*30*6, '/');
        }
        if (isset($_GET['dod'])) {
//            dd($_GET['dod']);
            setcookie('dropoff_date', $_GET['dod'], time() + 86400*30*6, '/');
        }
        if (isset($_GET['dot'])) {
//            dd($_GET['dot']);
            setcookie('dropoff_time', $_GET['dot'], time() + 86400*30*6, '/');
        }
    }

}
