<?php

namespace App\Http\Controllers;

use App\Post;
use App\Tag;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\View;
use App\Category;
use Spatie\SchemaOrg\Schema;
use CyrildeWit\EloquentViewable\Support\Period;

class PostsController extends Controller {

    public function __construct()
    {
        parent::__construct();

        // canonical url initialization
        $this->view_data['canonical_url'] = Request::url();
    }

    /**
     * Display a post of the resource.
     *
     */
    public function index(\Illuminate\Http\Request $request)
    {
        return $this->handleIndex($request, false);
    }

    /**
     * Display a post of the resource.
     *
     */
    public function indexWithTags($tag, \Illuminate\Http\Request $request)
    {
        return $this->handleIndex($request, true);
    }


    /**
     * Display the specified resource.
     *
     * @param  int $id
     */
    public function show($slug)
    {
    	$this->view_data['post_list_selected'] = 'current';
    	// Get post with the selected translation based on slug
        $post = Post::whereTranslation('slug', $slug)
            ->published()
            ->firstOrFail();

        // if post is not translated in current locale
        // ...temporarily redirect to blog homepage of locale
        if ( !$post->translate() )
        {
            return redirect()->route('posts.index');
        }

        // align the url locale with the slug locale
        if ($post->translate()->where('slug', $slug)->first()->locale != app()->getLocale())
        {
            return redirect()->route('posts.show', $post->translate()->slug);
        }

//        // if the post is not translated in the current locale, redirect to blog home
//        if( ! $post->hasTranslation($this->view_data['current_locale']) )
//        {
//            return redirect()->route('posts.index');
//        }

        // Load model again with all translations
        $post = Post::find($post->id);

		$this->view_data['post']                    = $post;
		$this->view_data['main_photo']              = $post->getFirstMedia('photos');
		$this->view_data['main_photo_attributes']   = empty($this->view_data['main_photo']) ? array() : $this->view_data['main_photo']->getSeoAttributes();

		$featuredListings = array();
    	$featuredCategory = Category::find(1);
    	if($featuredCategory){
    		$featuredListings = $featuredCategory->listings()->getResults();
    	}

		$this->view_data['featuredListings'] = $featuredListings;

        $this->view_data['related_posts']   = $post->getMotifedPosts();

        $this->view_data['url_ref']         = 'blog';

        // seo stuff
        // canonical url is set on constructor level

        // opengraph
        $this->overwriteOpengraph(
            $post->meta_title,
            $post->meta_description,
            $this->view_data['main_photo'] ? $this->view_data['main_photo']->getFullUrl() : null,
            'article'
        );

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => route('posts.index'),
                                'name' => trans('breadcrumbs.blog'),
                            )
                        ),
                    Schema::listItem()
                        ->position(3)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => $post->title,
                            )
                        ),
                )
            );

        // record a view fer teh post
        views($post)->record();

		return View::make('frontend.posts.show', $this->view_data);
    }

    private function handleIndex(\Illuminate\Http\Request $request, $withTags = false)
    {
        // fetch all posts to be shown
        if($withTags) // index of posts for specific tag
        {
            // get the tag
            $tag = Tag::where('slug', $request->route('tag'))
                ->firstOrFail();
            // Base query
            $posts = $tag->posts();

            // page meta title, meta description and heading
            $this->view_data['page_title']          = sprintf(trans('posts.index_tag_page_title'), $tag->title);
            $this->view_data['page_heading']        = sprintf(trans('posts.index_tag_page_heading'), $tag->title);
            $this->view_data['page_description']    = sprintf(trans('posts.index_tag_page_description'), $tag->title);
        }
        else // vanilla index page
        {
            // Base query
            $posts = Post::query();

            // page meta title, meta description and heading
            $this->view_data['page_title']          = trans('posts.index_vanilla_page_title');
            $this->view_data['page_heading']        = trans('posts.index_vanilla_page_heading');
            $this->view_data['page_description']    = trans('posts.index_vanilla_page_description');
        }

        // Add global search params
        $posts
            ->translatedIn($this->view_data['current_locale'])
            ->withTranslation()
            ->with('tags')
            ->published()
            ->orderBy('created_at', 'desc');

        // we set the posts variable
        $this->view_data['blogPosts'] = $posts->paginate(10);

        $this->view_data['post_list_selected'] = 'current';

        // tags fer tag cloud
        $this->view_data['tags'] = Tag::withCount('posts')
            ->orderBy('posts_count', 'desc')
            ->take(11)
            ->get();
        $this->view_data['featured_posts']  = Post::published()
//            ->featured()
            ->translatedIn($this->view_data['current_locale'])
            ->orderByViews('desc', Period::pastWeeks(3))
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // seo stuff
        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => trans('breadcrumbs.blog'),
                            )
                        ),
                )
            );

        // flag about having tag or not
        $this->view_data['with_tags'] = $withTags;

        // opengraph
        $this->overwriteOpengraph(
            $this->view_data['page_title'],
            $this->view_data['page_description']
        );

        return View::make('frontend.posts.index', $this->view_data);
    }
}
