@extends('layouts.admin.base')

@section('body')
    <section class="stripes padding">
        <div class="container">
            <div class="desktop-12   padding-bottom-small columns">
                <h1 class="text bold condensed">Edit pricing</h1>

                <h2>Group {{ $group->name }} ({{ $group->description }})</h2>
            </div>
        </div>
    </section>

    <div class="main">
        {!! csrf_field() !!}
        <div class="container">
            <div class="desktop-3 columns">
                <section class="padding">
                    <label for="baseGroupPrice">Our cars price</label>
                        <div class="padding-bottom-tiny">
                            <span class="badge badge_site_1 on-left">Eurodollar</span>
                            <span class="badge badge_site_2 on-right">Cretan</span>
                        	<div class="clear"></div>
                        </div>
                        <div>
                        	<div class="group_input on-left">
                            	<input type="text" class="border_box_site_1" data-groupId="{{ $group->id }}" data-url="{{ route('hodor.groups.updateBasePrice', $group->id) }}" id="baseGroupPrice" name="baseGroupPrice" data-site="eurodollar" value="{{ $group->base_price }}" placeholder="Enter price (only integer)">
	                        </div>
                        	<div class="group_input on-right">
                            	<input type="text" class="border_box_site_2" data-groupId="{{ $group->id }}" data-url="{{ route('hodor.groups.updateBasePrice', $group->id) }}" id="baseGroupPriceCretan" name="baseGroupPriceCretan" data-site="cretanrentals" value="{{ $group->base_price_cretan }}" placeholder="Enter price (only integer)">
	                        </div>
                        	<div class="clear"></div>
                        </div>
                        <div class="clear"></div>
                </section>
            </div>
        </div>
        @for($i=1;$i<=count($prices);$i++)
            @if($i % 2 == 0)
                {!!   '<section class="padding-small">' !!}
            @else
                {!!'<section class="padding-small stripes">' !!}
            @endif
            <div class="container">
                <div class="desktop-12 columns">
                    <h3 class="text bold">{!! $prices[$i][0]->period->title !!}</h3>
                </div>
                @foreach($prices[$i] as $price)
                    <div class="desktop-3 columns">
                        <p>
                            <label for="pricingPeriod{{ $price->period->id }}" class="" style="font-size:14px">Pricing for {{ $price->period->alias }}</label>
                        </p>
                        <div class="padding-bottom-tiny">
                            <span class="badge badge_site_1 on-left">Eurodollar</span>
                            <span class="badge badge_site_2 on-right">Cretan</span>
                        	<div class="clear"></div>
                        </div>
                        <div>
                        	<div class="group_input on-left">
                            	<input type="text" class="pricingPeriod border_box_site_1" data-groupId="{{ $group->id }}" data-periodId="{{ $price->period->id }}" data-url="{{ route('admin.pricing.update') }}" data-site="eurodollar" name="pricingPeriod_{{ $price->period->id }}" value="{{ $price->price }}" data-old-value="{{ $price->price }}" placeholder="Enter price (only integer)" />
	                        </div>
                        	<div class="group_input on-right">
	                            <input type="text" class="pricingPeriod border_box_site_2" data-groupId="{{ $group->id }}" data-periodId="{{ $price->period->id }}" data-url="{{ route('admin.pricing.update') }}" data-site="cretanrentals" name="pricingPeriod_cretan_{{ $price->period->id }}" value="{{ $price->price_cretan }}" data-old-value="{{ $price->price_cretan }}" placeholder="Enter price (only integer)" />
	                        </div>
                        	<div class="clear"></div>
                        </div>
                        <div class="clear"></div>
                        <p>
                        	<label for="availablePeriod_{{ $price->period->id }}">Availability (0/1) {{ $price->period->alias }}</label>
                        </p>
                        <div class="clear"></div>
                        <div>
                        	<div class="switch_site_1 group_input on-left">
								<label class="switch on-left">
									<input type="checkbox" class="availabilityPeriod" data-groupId="{{ $group->id }}" data-periodId="{{ $price->period->id }}" data-url="{{ route('admin.availability.update') }}" data-site="eurodollar" id="availablePeriod_{{ $price->period->id }}" name="availablePeriod_{{ $price->period->id }}" value="{{ $price->available }}" data-old-value="{{ $price->available }}" {{ (int)$price->available === 1 ? 'checked="checked"' : '' }}/>
									<div class="slider round"></div>
	                        		<div class="clear"></div>
								</label>
                        	</div>
                        	<div class="switch_site_2 group_input on-right">
								<label class="switch on-right">
									<input type="checkbox" class="availabilityPeriod" data-groupId="{{ $group->id }}" data-periodId="{{ $price->period->id }}" data-url="{{ route('admin.availability.update') }}" data-site="cretanrentals" id="availablePeriod_cretan_{{ $price->period->id }}" name="availablePeriod_cretan_{{ $price->period->id }}" value="{{ $price->available_cretan }}" data-old-value="{{ $price->available_cretan }}"  {{ (int)$price->available_cretan === 1 ? 'checked="checked"' : '' }}/>
									<div class="slider round"></div>
	                        		<div class="clear"></div>
								</label>
                        	</div>
                        	<div class="clear"></div>
                        </div>
                        <div class="clear"></div>
                        <span class="error">{!! $errors->first($price->period->alias) !!}</span>
                    </div>
                @endforeach
            </div>
            </section>
        @endfor
    </div>
@stop
@section('footer_js')
@stop