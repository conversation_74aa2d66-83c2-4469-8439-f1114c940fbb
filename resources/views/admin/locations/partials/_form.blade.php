
<section class="padding-small">
	<div class="container">
		<div class="desktop-3 columns">
            <p>{!! Form::label('area', 'Area:') !!}</p>
            <p>{!! Form::text('area') !!}</p>
            <p>{!! $errors->first('area') !!}</p>
            <p>{!! Form::label('remote_location', 'Remote Location') !!}</p>
            <p>{!! Form::select('remote_location', ['0' => 'No', '1' => 'Yes']) !!}</p>
            <p>{!! Form::label('remote_location_short', 'Remote Location Short') !!}</p>
            <p>{!! Form::select('remote_location_short', ['0' => 'No', '1' => 'Yes']) !!}</p>
		</div>
		<div class="desktop-3 columns">
			<?php $origLocale = App::getLocale();?>
    		@foreach (Config::get('translationLocales') as $locale)
			<?php App::setLocale($locale);?>
     		<p>{!! Form::label('name_' . $locale, 'Name (' . $locale . '):') !!}</p>
            <p>{!! Form::text('name_' . $locale, isset($location) ? $location->name : '') !!}</p>
            <p>{!! $errors->first('name_' . $locale) !!}</p>
    		@endforeach
			<?php App::setLocale($origLocale);?>
		</div>
	</div>
</section>
<section class="padding-small stripes">
	<h2 class="align center padding-small">Pickup - Dropoff descriptions</h2>
	<div class="container">
		<div class="desktop-6 columns">
			<?php $origLocale = App::getLocale();?>
    		@foreach (Config::get('translationLocales') as $locale)
			<?php App::setLocale($locale);?>
     		<p>{!! Form::label('pickup_description_' . $locale, 'Pickup description (' . $locale . '):') !!}</p>
            <p>{!! Form::textarea('pickup_description_' . $locale, isset($location) ? $location->pickup_description : '') !!}</p>
            <p>{!! $errors->first('pickup_description_' . $locale) !!}</p>
    		@endforeach
			<?php App::setLocale($origLocale);?>
		</div>
		<div class="desktop-6 columns">
			<?php $origLocale = App::getLocale();?>
    		@foreach (Config::get('translationLocales') as $locale)
			<?php App::setLocale($locale);?>
     		<p>{!! Form::label('dropoff_description_' . $locale, 'Dropoff description (' . $locale . '):') !!}</p>
            <p>{!! Form::textarea('dropoff_description_' . $locale, isset($location) ? $location->dropoff_description : '') !!}</p>
            <p>{!! $errors->first('dropoff_description_' . $locale) !!}</p>
    		@endforeach
			<?php App::setLocale($origLocale);?>
		</div>
	</div>
</section>
<section class="padding">
	<div class="container">
		<div class="desktop-6 offset-3 columns">
    		<p>{!! Form::submit('Save', ['class' => 'button']) !!}</p>
		</div>

	</div>
</section>
