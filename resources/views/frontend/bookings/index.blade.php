@extends('layouts.frontend.base')

@section('title'){!! trans('bookings.landing.meta_title') !!}@stop

@section('meta_description'){!! trans('bookings.landing.meta_description') !!}@stop

@section('head_extra')
    <link rel="canonical" href="{{ $canonical_url }}" />
    <meta name="robots" content="noindex">
@stop

@section('body')
{{--    @php--}}
{{--        $pickup_location = isset($_COOKIE['pickup_location'])--}}
{{--            ? $locations_dropdown_pickup[$_COOKIE['pickup_location']]--}}
{{--            : $locations_dropdown_pickup[1];--}}
{{--        $dropoff_location = isset($_COOKIE['dropoff_location'])--}}
{{--            ? $locations_dropdown_pickup[$_COOKIE['dropoff_location']]--}}
{{--            : $pickup_location;--}}
{{--    @endphp--}}

    <section class="section section-car-listing">
        <div class="container container-1291">


            <div class="aside">
                <div class="booking-details">
                    <h3>{{ trans('bookings.fleet.page_title') }}</h3>

                    <div>
                        <x-icons.map-mark style="width:20px;" />

                        {{ $locations_dropdown_pickup[$reservation_data['pickup_location']] }} <br>
                        <span class="date-time">{{ $reservation_data['pickup_date'] }} {{ $reservation_data['pickup_time'] }}</span>
                    </div>

                    <div class="distance"></div>
                    <div>
                        <x-icons.map-mark style="width:20px;" :filled="true" />

                        {{ $locations_dropdown_pickup[$reservation_data['dropoff_location']] }} <br>
                        <span class="date-time">{{ $reservation_data['dropoff_date'] }} {{ $reservation_data['dropoff_time'] }}</span>
                    </div>

                    <div class="change-details">
                        <a href="{{ route('booking.show') }}" class="change-cta">
                            {{ __('Change') }}
                        </a>
                    </div>
                </div>
            </div>

            <div class="car-list-container">

                <div class="filter-container" x-data="{ show: false }" @click.outside="show = false">
                    <div @click="show = !show">
                        <span>{{ trans('listings.car_type') }}</span>
                        <img x-show="!show" src="{{ asset('/images/chevron-down.svg') }}"alt="arrow-down"
                            style="transform: rotate(0deg);">
                        <img x-show="show" src="{{ asset('/images/chevron-down.svg') }}" alt="arrow-down"
                            style="transform: rotate(180deg);">

                    </div>
                    <div x-show="show" id="supergroup-filter">
                        <div class="filter-button active" data-supergroup="0">{{ trans('common.all_cars') }}</div>

                        @foreach ($supergroups as $supergroup_id => $supergroup_title)
                            <div class="filter-button" @click="show = false" data-supergroup="{{ $supergroup_id }}">{{ $supergroup_title }}
                            </div>
                        @endforeach
                    </div>
                </div>

                @forelse($listings as $listing)
                    <?php $discount = new \App\Services\Offer\StaticDiscount($date_range->getTotalDays(), $date_range->getDaysIndex(), $listing); ?>
                    <?php $offer = $listing->getOffer($date_range, $discount, Arr::get($_COOKIE, 'pickup_location', ''), $dropoff_location_for_offer_calculation); ?>
                    @include('frontend.bookings.partials._listing_list_item')
                @empty
                    <p class="normal-paragraph" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
                        {{ trans('listings.no_cars_found') }}</p>
                @endforelse
            </div>
        </div>
    </section>

@stop

@section('footer_js')
    <script>
        // Suppergroup filter

        $('#supergroup-filter .filter-button').on('click', function() {

            var selectedSuperGroup = $(this).data('supergroup');

            // Remove 'active' class from all filter buttons
            $('#supergroup-filter .filter-button').removeClass('active');

            // Add 'active' class to the clicked filter button
            $(this).addClass('active');

            if (selectedSuperGroup != 0) {
                $('.car-list-item').show().filter(function() {
                    var itemSuperGroup = $(this).data('supergroup');

                    return selectedSuperGroup !== "" && itemSuperGroup != selectedSuperGroup;

                }).hide();
            } else {
                $('.car-list-item').show();
            }
        });
    </script>
@endsection
