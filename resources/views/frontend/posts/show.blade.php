@extends('layouts.frontend.base')

@section('title')
    {{ $post->meta_title }}
@stop

@section('meta_description')
    {{ $post->meta_description }}
@stop

@section('head_extra')
    <link rel="canonical" href="{{ $canonical_url }}" />
@stop

@section('body')
    <div>
        {!! Form::open(['url' => route('booking.handle'), 'class' => ['main', 'search-form'], 'id' => 'booking_form']) !!}
        @include('frontend.posts.partials._search_form_blog_top')
        {!! Form::close() !!}
    </div>
    <section class="section blog blog-post">

        <div class="container">

            <div class="search-row">
                <div class="breadcrumbs" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
                    <a href="{!! route('home') !!}">
                        <img src="{!! asset('images/home.svg') !!}" alt="{!! trans('breadcrumbs.home') !!}" /> {!! trans('breadcrumbs.home') !!}
                    </a>
                    <span>‣</span>
                    <a href="{!! route('posts.index') !!}">{!! trans('common.blog') !!}</a>
                    <span>‣</span>
                    <span>{!! $post->title !!}</span>
                    {!! $breadcrumbs_schema !!}
                </div>
                <div>
                    <form action="{{ route('search.index') }}">
                        <div class="search-wrapper">
                            <input class="search-input" type="text" name="q" placeholder="search"
                                value="{{ request('q') }}">
                            <button class="search-button">
                                <x-icons.search-icon />
                            </button>
                        </div>
                    </form>
                </div>
            </div>



            <h1>{!! $post->title !!}</h1>

            <div class="main-content">

                <div class="translations">
                    {{ $post->translations->count() > 1 ? 'Also available in:' : '' }}
                    @foreach ($post->translations as $translation)
                        @if ($translation->locale !== Lang::locale())
                            <div class="language">
                                <a href="/{{ $translation->locale }}/blog/{{ $translation->slug }}">
                                    <img src="/images/icons/flag-{{ $translation->locale }}.png"
                                        alt="{{ $translation->locale }} flag">
                                    {{ trans('common.country_locales.' . $translation->locale) }}
                                </a>

                            </div>
                        @endif
                    @endforeach
                </div>

                <div class="publish-info">
                    {!! trans('blog.published_at') !!} {{ \Carbon\Carbon::parse($post->created_at)->format('d-m-Y') }} -
                    {!! trans('blog.updated_at') !!} {{ \Carbon\Carbon::parse($post->updated_at)->format('d-m-Y') }}
                </div>
                @if (!empty($main_photo))
                    <div class="main-image">
                        {{ $main_photo->img()->attributes($main_photo_attributes) }}
                    </div>
                @endif
                <div class="body">
                    <p>{!! $curated_content !!}</p>
                </div>
                @if ($post->tags->count())
                    <div class="tag-list">
                        @foreach ($post->tags as $tag)
                            <div class="tag">
                                <a href="{{ route('post.tags.index', $tag->slug) }}">{{ $tag->title }}</a>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            @include('frontend.posts.partials._related_posts', ['related_posts' => $related_posts])

            @include('frontend.posts.partials._suggested_posts')

            <input type="text" id="datepicker" class="sessionable" style="display: none;"
                data-lang={{ $current_locale }} />

        </div>
    </section>

    @include('frontend.partials._featuredListings', ['show_button' => true])

@stop
