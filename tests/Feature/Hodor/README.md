# Budget Per Day Analytics Tests

This directory contains tests for the Budget Per Day Analytics feature in the Hodor module.

## Test Files

1. **BudgetPerDayPageTest.php**: Feature tests for page accessibility and loading
2. **BudgetPerDayControllerTest.php**: Integration tests for the controller functionality
3. **BudgetPerDayCalculationTest.php** (in tests/Unit/Hodor/): Unit tests for the calculation logic

## Running the Tests

To run all the tests for the Budget Per Day feature:

```bash
php artisan test tests/Feature/Hodor/BudgetPerDayPageTest.php tests/Feature/Hodor/BudgetPerDayControllerTest.php tests/Unit/Hodor/BudgetPerDayCalculationTest.php
```

To run just the feature tests:

```bash
php artisan test tests/Feature/Hodor/BudgetPerDayPageTest.php
```

To run just the controller tests:

```bash
php artisan test tests/Feature/Hodor/BudgetPerDayControllerTest.php
```

To run just the calculation unit tests:

```bash
php artisan test tests/Unit/Hodor/BudgetPerDayCalculationTest.php
```

## Test Coverage

These tests cover:

1. **Page Accessibility**: Tests that the budget per day page loads correctly with different parameters
2. **Controller Functionality**: Tests that the controller returns the correct view data
3. **Calculation Logic**: Tests that the budget per day calculations are correct

The tests use controlled test data to verify that:

- Monthly totals are calculated correctly
- Group-segmented data is calculated correctly
- Yearly totals are calculated correctly
- Average price per day is calculated correctly (final_price / total_days)
- "No show" reservations are excluded from calculations
- Different year parameters are handled correctly
