<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BookingsControllerUrlParamsTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that URL parameters are immediately reflected in the view data on first refresh
     */
    public function test_url_params_immediately_update_view_data(): void
    {
        // Test with URL parameters that should override any existing cookies
        $response = $this->get('/book/fleet?' . http_build_query([
            'pul' => '2',           // pickup_location
            'pud' => '15-06-2024',  // pickup_date
            'put' => '14:00',       // pickup_time
            'dol' => '3',           // dropoff_location
            'dod' => '22-06-2024',  // dropoff_date
            'dot' => '16:00',       // dropoff_time
        ]));

        $response->assertStatus(200);
        
        // Check that the view has the correct reservation_data with URL param values
        $response->assertViewHas('reservation_data', [
            'pickup_location'  => '2',
            'pickup_date'      => '15-06-2024',
            'pickup_time'      => '14:00',
            'dropoff_location' => '3',
            'dropoff_date'     => '22-06-2024',
            'dropoff_time'     => '16:00',
        ]);

        // Also check that the view_data has been updated with current values
        $response->assertViewHas('pickup_date', '15-06-2024');
        $response->assertViewHas('pickup_time', '14:00');
        $response->assertViewHas('dropoff_date', '22-06-2024');
        $response->assertViewHas('dropoff_time', '16:00');
    }

    /**
     * Test that the fleet index page works without URL parameters (using defaults)
     */
    public function test_fleet_index_works_with_defaults(): void
    {
        $response = $this->get('/book/fleet?x-bkng-rdrct=1');

        $response->assertStatus(200);
        $response->assertViewHas('reservation_data');
        
        // Should have default values
        $reservationData = $response->viewData('reservation_data');
        $this->assertArrayHasKey('pickup_location', $reservationData);
        $this->assertArrayHasKey('pickup_date', $reservationData);
        $this->assertArrayHasKey('pickup_time', $reservationData);
        $this->assertArrayHasKey('dropoff_location', $reservationData);
        $this->assertArrayHasKey('dropoff_date', $reservationData);
        $this->assertArrayHasKey('dropoff_time', $reservationData);
    }

    /**
     * Test that partial URL parameters work correctly (some from URL, some from defaults)
     */
    public function test_partial_url_params_work_correctly(): void
    {
        $response = $this->get('/book/fleet?' . http_build_query([
            'pul' => '2',           // Only pickup_location from URL
            'pud' => '15-06-2024',  // Only pickup_date from URL
            // Other params should use defaults
        ]));

        $response->assertStatus(200);
        
        $reservationData = $response->viewData('reservation_data');
        
        // Should have URL param values
        $this->assertEquals('2', $reservationData['pickup_location']);
        $this->assertEquals('15-06-2024', $reservationData['pickup_date']);
        
        // Should have default values for others
        $this->assertEquals('12:00', $reservationData['pickup_time']);
        $this->assertEquals('12:00', $reservationData['dropoff_time']);
    }

    /**
     * Test that the DateRange is created with current effective values
     */
    public function test_date_range_uses_current_effective_values(): void
    {
        $response = $this->get('/book/fleet?' . http_build_query([
            'pud' => '15-06-2024',  // pickup_date
            'put' => '14:00',       // pickup_time
            'dod' => '22-06-2024',  // dropoff_date
            'dot' => '16:00',       // dropoff_time
        ]));

        $response->assertStatus(200);
        $response->assertViewHas('date_range');
        
        $dateRange = $response->viewData('date_range');
        $this->assertInstanceOf(\App\Services\Offer\DateRange::class, $dateRange);
    }
}
