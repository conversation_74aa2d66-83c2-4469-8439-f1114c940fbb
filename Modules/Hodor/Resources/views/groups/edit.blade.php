@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.groups.texts.edit', $group->id) }}" class="btn btn-outline-info">Edit group texts</a>
            </div>
        </div>
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Edit Group: {{ $group->name }}</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::model($group, ['method' => 'PUT', 'class' => 'main', 'route' => ['hodor.groups.update', $group->id]]) !!}
            {!! Form::token() !!}
            <div class="card-body">
                @include('hodor::groups._form')
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Group
                </button>
                <a href="{{ route('hodor.groups.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Groups
                </a>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection

@section('js')
<script>
$(document).ready(function() {
    $('.select2').select2();
});
</script>
@stop
