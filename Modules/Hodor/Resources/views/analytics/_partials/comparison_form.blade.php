<section class="content">
    <form method="POST" action="{{ route('hodor.analytics.budget.comparisons.submit') }}">
        @csrf
        <div class="row mb-3">
            <div class="col-lg-6">
                <h5>Reference Period</h5>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="date_from">From Date:</label>
                            {{ Form::text('date_from', $date_from ?? Carbon\Carbon::createFromDate(Carbon\Carbon::now()->year, 1, 1)->format('Y-m-d'), ['class' => 'form-control', 'id' => 'date_from_picker', 'required', 'readonly']) }}
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="date_to">To Date:</label>
                            {{ Form::text('date_to', $date_to ?? Carbon\Carbon::now()->format('Y-m-d'), ['class' => 'form-control', 'id' => 'date_to_picker', 'required', 'readonly']) }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <h5>Comparison Period</h5>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="comparison_from">From Date:</label>
                            {{ Form::text('comparison_from', $comparison_from ?? Carbon\Carbon::createFromDate(Carbon\Carbon::now()->subYear()->year, 1, 1)->format('Y-m-d'), ['class' => 'form-control', 'id' => 'comparison_from_picker', 'required', 'readonly']) }}
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="comparison_to">To Date:</label>
                            {{ Form::text('comparison_to', $comparison_to ?? Carbon\Carbon::now()->subYear()->format('Y-m-d'), ['class' => 'form-control', 'id' => 'comparison_to_picker', 'required', 'readonly']) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4">
                <button type="submit" class="btn btn-primary">Calculate&nbsp;&nbsp;&nbsp;<i class="fas fa-calculator"></i></button>
            </div>
        </div>
    </form>
</section>


