

<div class="row mb-4">
    <div class="col-12">
        <div class="card card-purple">
            <div class="card-header">
                <h3 class="card-title">Reference Period: {{ $date_from }} to {{ $date_to }}</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @if(isset($analytics_data['counts']['total_reservations_reference']))
                        <div class="col-md-3">
                            <div class="small-box bg-purple">
                                <div class="inner">
                                    <h3>@number_present($analytics_data['counts']['total_reservations_reference'])</h3>
                                    <p>Reservations</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-list"></i>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($analytics_data['counts']['total_revenue_reference']))
                        <div class="col-md-3">
                            <div class="small-box bg-purple">
                                <div class="inner">
                                    <h3>@money($analytics_data['counts']['total_revenue_reference'])</h3>
                                    <p>Revenue</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-euro-sign"></i>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($analytics_data['counts']['total_days_reference']))
                        <div class="col-md-3">
                            <div class="small-box bg-purple">
                                <div class="inner">
                                    <h3>@number_present($analytics_data['counts']['total_days_reference'])</h3>
                                    <p>Total Days</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-calendar-day"></i>
                                </div>
                            </div>
                        </div>
                    @endif
{{--                    @if(isset($analytics_data['counts']['avg_revenue_per_day_reference']))--}}
{{--                        <div class="col-md-3">--}}
{{--                            <div class="small-box bg-purple">--}}
{{--                                <div class="inner">--}}
{{--                                    <h3>@money_decimal($analytics_data['counts']['avg_revenue_per_day_reference'])</h3>--}}
{{--                                    <p>Avg Revenue Per Day</p>--}}
{{--                                </div>--}}
{{--                                <div class="icon">--}}
{{--                                    <i class="fas fa-fw fa-chart-line"></i>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    @endif--}}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">Comparison Period: {{ $comparison_from }} to {{ $comparison_to }}</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @if(isset($analytics_data['counts']['total_reservations_comparison']))
                        <div class="col-md-3">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>@number_present($analytics_data['counts']['total_reservations_comparison'])</h3>
                                    <p>Reservations</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-list"></i>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($analytics_data['counts']['total_revenue_comparison']))
                        <div class="col-md-3">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>@money($analytics_data['counts']['total_revenue_comparison'])</h3>
                                    <p>Revenue</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-euro-sign"></i>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($analytics_data['counts']['total_days_comparison']))
                        <div class="col-md-3">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>@number_present($analytics_data['counts']['total_days_comparison'])</h3>
                                    <p>Total Days</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-fw fa-calendar-day"></i>
                                </div>
                            </div>
                        </div>
                    @endif
{{--                    @if(isset($analytics_data['counts']['avg_revenue_per_day_comparison']))--}}
{{--                        <div class="col-md-3">--}}
{{--                            <div class="small-box bg-info">--}}
{{--                                <div class="inner">--}}
{{--                                    <h3>@money_decimal($analytics_data['counts']['avg_revenue_per_day_comparison'])</h3>--}}
{{--                                    <p>Avg Revenue Per Day</p>--}}
{{--                                </div>--}}
{{--                                <div class="icon">--}}
{{--                                    <i class="fas fa-fw fa-chart-line"></i>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    @endif--}}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card {{ $analytics_data['counts']['diff_reservations'] >= 0 ? 'card-success' : 'card-danger' }}">
            <div class="card-header">
                <h3 class="card-title">Differences (Comparison vs Reference)</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="small-box {{ $analytics_data['counts']['diff_reservations'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                            <div class="inner">
                                <h3>@number_present($analytics_data['counts']['diff_reservations']) (@number_present($analytics_data['counts']['pct_reservations'])%)</h3>
                                <p>Reservations Difference</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-fw {{ $analytics_data['counts']['diff_reservations'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }}"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="small-box {{ $analytics_data['counts']['diff_revenue'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                            <div class="inner">
                                <h3>@money($analytics_data['counts']['diff_revenue']) (@number_present($analytics_data['counts']['pct_revenue'])%)</h3>
                                <p>Revenue Difference</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-fw {{ $analytics_data['counts']['diff_revenue'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }}"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="small-box {{ $analytics_data['counts']['diff_days'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                            <div class="inner">
                                <h3>@number_present($analytics_data['counts']['diff_days']) (@number_present($analytics_data['counts']['pct_days'])%)</h3>
                                <p>Total Days Difference</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-fw {{ $analytics_data['counts']['diff_days'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }}"></i>
                            </div>
                        </div>
                    </div>
{{--                    @if(isset($analytics_data['counts']['diff_avg_revenue_per_day']))--}}
{{--                    <div class="col-md-3">--}}
{{--                        <div class="small-box {{ $analytics_data['counts']['diff_avg_revenue_per_day'] >= 0 ? 'bg-success' : 'bg-danger' }}">--}}
{{--                            <div class="inner">--}}
{{--                                <h3>@money_decimal($analytics_data['counts']['diff_avg_revenue_per_day']) (@number_present($analytics_data['counts']['pct_avg_revenue_per_day'])%)</h3>--}}
{{--                                <p>Avg Revenue Per Day Diff</p>--}}
{{--                            </div>--}}
{{--                            <div class="icon">--}}
{{--                                <i class="fas fa-fw {{ $analytics_data['counts']['diff_avg_revenue_per_day'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }}"></i>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    @endif--}}
                </div>
            </div>
        </div>
    </div>
</div>

