<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class GroupTextUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        foreach (array_keys(config('laravellocalization.supportedLocales')) as $langKey){
            $rules[$langKey] = [];
            if(
                empty($this->{$langKey . '.description'}) &&
                empty($this->{$langKey . '.description_cr'}) &&
                empty($this->{$langKey . '.seo_text'}) &&
                empty($this->{$langKey . '.seo_text_cr'})
            )
            {
                $this->merge([
                    $langKey => [],
                ]);
            }
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];

        foreach (array_keys(config('laravellocalization.supportedLocales')) as $langKey){
            $rules[$langKey . '.description']      = 'required';
            $rules[$langKey . '.description_cr']   = 'sometimes';
            $rules[$langKey . '.seo_text']         = 'sometimes';
            $rules[$langKey . '.seo_text_cr']      = 'sometimes';
        }
        return $rules;
    }
}
