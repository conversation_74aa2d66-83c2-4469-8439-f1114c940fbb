<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;
use Illuminate\Support\Facades\Config;

class GroupUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name'              => 'required',
            'engine'            => 'required',
            'transmission'      => 'required',
            'capacity'          => 'required',
            'seats'             => 'required',
            'doors'             => 'required',
            'offer_percentage'  => 'required|numeric|min:1|max:100',
            'fuel_plan'         => 'required|integer|min:0',
            'base_price'        => 'required|integer|min:1',
            'base_price_cretan' => 'required|integer|min:1',
            'fuel'              => 'nullable|in:petrol,diesel',
            'on_offer'          => 'nullable|boolean',
            'or_similar'        => 'nullable|boolean',
            'supergroup_id'     => 'required|exists:supergroups,id',
            'related_group'     => 'nullable|array',
            'related_group.*'   => 'exists:groups,id',
            'excess'            => 'required|integer|min:0',
        ];

        return $rules;
    }
}
