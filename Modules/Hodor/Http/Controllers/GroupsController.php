<?php

namespace Modules\Hodor\Http\Controllers;

use App\Group;
use App\Period;
use App\Supergroup;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Modules\Hodor\Http\Requests\GroupStoreRequest;
use Modules\Hodor\Http\Requests\GroupUpdateRequest;
use Modules\Hodor\Http\Requests\GroupTextUpdateRequest;
use Modules\Hodor\Http\Requests\GroupBasePriceUpdateRequest;

class GroupsController extends HodorController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        $this->view_data['groups'] = Group::orderBy('group_order', 'asc')
            ->orderBy('id', 'asc')
            ->get();
        $this->view_data['page_title'] = 'Car Groups';

        return view('hodor::groups.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']          = 'Create New Group';
        $this->view_data['group_list']          = Group::orderByRaw('group_order ASC, id ASC')->get()->pluck('fullName', 'id')->toArray();
        $this->view_data['related_groups_list'] = null;
        $this->view_data['supergroup_list']     = Supergroup::pluck('name', 'id')->toArray();

        return view('hodor::groups.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param GroupStoreRequest $request
     * @return Renderable
     */
    public function store(GroupStoreRequest $request)
    {
        $group = Group::create($request->validated());

        // Handle related groups (from performCustomPostCreationTasks)
        if (!empty($request->input('related_group'))) {
            $group->relatedGroups()->attach($request->input('related_group'));
        }

        // Initialize price in group_period pivot table for every available period
        $periods = Period::all();
        foreach ($periods as $period) {
            $group->periods()->attach($period->id, ['price' => 0]);
        }

        return redirect()->route('hodor.groups.edit', $group->id)
            ->withSuccess('Group with id: ' . $group->id . ' was successfully created!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $group = Group::findOrFail($id);

        $this->view_data['group'] = $group;
        $this->view_data['page_title'] = 'Edit Group: ' . $group->name;
        $this->view_data['group_list'] = Group::orderByRaw('group_order ASC, id ASC')->pluck('name', 'id')->toArray();
        $this->view_data['supergroup_list'] = Supergroup::pluck('name', 'id')->toArray();

        // Get related groups
        $relatedGroups = [];
        foreach ($group->relatedGroups as $related) {
            $relatedGroups[] = $related->id;
        }
        $this->view_data['related_groups_list'] = $relatedGroups;

        return view('hodor::groups.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param GroupUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(GroupUpdateRequest $request, $id)
    {
        $group = Group::findOrFail($id);

        $group->update($request->validated());

        // Handle related groups (from performCustomUpdateTasks)
        if (!empty($request->input('related_group'))) {
            $group->relatedGroups()->sync($request->input('related_group'));
        } else {
            $group->relatedGroups()->sync([]);
        }

        return redirect()->route('hodor.groups.edit', $group->id)
            ->withSuccess('Group with id: ' . $group->id . ' was successfully updated!');
    }

    /**
     * Update the base price of the specified group via AJAX.
     * @param GroupBasePriceUpdateRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBasePrice(GroupBasePriceUpdateRequest $request, $id)
    {
        $group = Group::findOrFail($id);

        $priceField = 'base_price' . ($request->input('site') === 'cretanrentals' ? '_cretan' : '');
        $group->update([$priceField => $request->input('newValue')]);

        return response()->json([
            'status' => true,
            'message' => 'Price Updated',
        ]);
    }

    /**
     * Show the form for editing the specified resource texts.
     * @param int $id
     * @return Renderable
     */
    public function textEdit(int $id)
    {
        $group = Group::findOrFail($id);

        $this->view_data['group']       = $group;
        $this->view_data['page_title']  = $group->name . ': Edit texts';

        // languages
        $this->view_data['languages'] = Config::get('translationLocales');

        return view('hodor::groups.edit_texts', $this->view_data);
    }

    /**
     * Update the specified resource texts in storage.
     * @param GroupTextUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function textUpdate(GroupTextUpdateRequest $request, $id)
    {
        $group = Group::findOrFail($id);

        foreach (Config::get('translationLocales') as $locale)
        {
            // description
            if($request->input("{$locale}.description"))
            {
                $group->translateOrNew($locale)->description = $request->input("{$locale}.description");
            }
            // seo_text
            if($request->input("{$locale}.seo_text"))
            {
                $group->translateOrNew($locale)->seo_text = $request->input("{$locale}.seo_text");
            }
            // description_cr
            if($request->input("{$locale}.description_cr"))
            {
                $group->translateOrNew($locale)->description_cr = $request->input("{$locale}.description_cr");
            }
            // seo_text_cr
            if($request->input("{$locale}.seo_text_cr"))
            {
                $group->translateOrNew($locale)->seo_text_cr = $request->input("{$locale}.seo_text_cr");
            }
        }

        $group->save();

        // hack to bypass the issue of updating the updated_at filed on translation change
        $group->touch();

        return redirect()->route('hodor.groups.texts.edit', $group->id)
            ->withSuccess('The texts of group with id: ' . $group->id . ' successfully updated!');
    }

    /**
     * Set the sorting of the groups
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sortOrder(Request $request)
    {
        try {
            if ($request->ajax() && $request->input('groupID') && is_array($request->input('groupID'))) {
                foreach ($request->input('groupID') as $order => $groupID) {
                    $group = Group::find($groupID);
                    if (empty($group)) {
                        continue;
                    }
                    $group->group_order = $order;
                    $group->save();
                }

                return response()->json([
                    'status' => true,
                    'message' => 'The group order has been saved.',
                ]);
            }
        } catch (\Exception $e) {
            // Error saving group sorting
        }

        return response()->json([
            'status' => false,
            'message' => 'Error saving group sorting.',
        ]);
    }
}
